import React, { useState, useEffect } from 'react'
import { Download, Eye, CheckCircle, AlertCircle, Clock, RefreshCw, DownloadCloud, FileText } from 'lucide-react'
import { useApi } from '../contexts/ApiContext'
import './BatchResults.css'

const BatchResults = ({ taskIds, onClose }) => {
  const { apiBase } = useApi()
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // 获取批处理结果
  useEffect(() => {
    if (!taskIds || taskIds.length === 0) return

    console.log('🔍 BatchResults 收到任务IDs:', taskIds)
    let pollInterval = null

    const fetchResults = async () => {
      setLoading(true)
      setError(null)

      try {
        console.log('🔍 查询任务状态，IDs:', taskIds)
        const response = await fetch(`${apiBase}/process/results`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ task_ids: taskIds }),
          mode: 'cors'
        })

        if (response.ok) {
          const data = await response.json()
          console.log('🔍 API响应数据:', data)
          console.log('🔍 结果列表:', data.results)
          setResults(data.results || [])

          // 检查是否还有未完成的任务
          const hasIncompleteTask = data.results?.some(result =>
            result.status === 'not_ready' ||
            result.status === 'PENDING' ||
            result.status === 'PROGRESS'
          )

          if (hasIncompleteTask) {
            console.log('🔍 发现未完成任务，5秒后重新查询')
            // 5秒后重新查询
            pollInterval = setTimeout(fetchResults, 5000)
          } else {
            console.log('🔍 所有任务已完成')
            setLoading(false)
          }
        } else {
          throw new Error(`HTTP ${response.status}`)
        }
      } catch (error) {
        setError(`获取结果失败: ${error.message}`)
        setLoading(false)
      }
    }

    fetchResults()

    // 清理函数
    return () => {
      if (pollInterval) {
        clearTimeout(pollInterval)
      }
    }
  }, [taskIds, apiBase])

  const getStatusIcon = (status) => {
    switch (status) {
      case 'SUCCESS':
      case 'success':
        return <CheckCircle className="status-icon success" />
      case 'FAILURE':
      case 'failed':
      case 'error':
        return <AlertCircle className="status-icon error" />
      case 'PENDING':
      case 'RETRY':
      case 'not_ready':
        return <Clock className="status-icon pending" />
      default:
        return <RefreshCw className="status-icon processing" />
    }
  }

  const getStatusClass = (status) => {
    switch (status) {
      case 'SUCCESS':
      case 'success':
        return 'success'
      case 'FAILURE':
      case 'failed':
      case 'error':
        return 'error'
      case 'PENDING':
      case 'RETRY':
      case 'not_ready':
        return 'pending'
      default:
        return 'processing'
    }
  }

  const handleDownload = async (filename) => {
    try {
      const response = await fetch(`${apiBase}/download/${filename}`, {
        method: 'GET',
        mode: 'cors'
      })
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        throw new Error(`下载失败: HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('下载错误:', error)
    }
  }

  // 一键全部下载功能
  const handleDownloadAll = async () => {
    const successResults = results.filter(r =>
      (r.status === 'SUCCESS' || r.status === 'success') &&
      (r.result?.output_filename || r.output_filename)
    )

    if (successResults.length === 0) {
      alert('没有可下载的文件')
      return
    }

    try {
      // 收集所有文件名
      const filenames = successResults.map(result =>
        result.result?.output_filename || result.output_filename
      ).filter(Boolean)

      // 调用批量下载API
      const response = await fetch(`${apiBase}/download`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ filenames }),
        mode: 'cors'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'batch_download.zip'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        throw new Error(`HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('批量下载错误:', error)
      alert('批量下载失败，请重试')
    }
  }

  const handlePreview = (filename) => {
    const previewUrl = `${apiBase}/preview/${filename}`
    window.open(previewUrl, '_blank')
  }

  // 原文件信息组件
  const OriginalFilesInfo = ({ originalFiles }) => {
    if (!originalFiles || originalFiles.length === 0) {
      return <span>未知</span>
    }

    if (originalFiles.length === 1) {
      return <span>{originalFiles[0]}</span>
    }

    return (
      <div className="original-files-tooltip">
        <span className="files-count">
          {originalFiles.length} 个文件
        </span>
        <div className="tooltip-content">
          <div className="files-list">
            {originalFiles.map((filename, index) => (
              <div key={index} className="file-item">
                {filename}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // 图片预览组件
  const ImagePreview = ({ filename }) => {
    const [imageError, setImageError] = useState(false)

    if (imageError) {
      return (
        <div className="preview-placeholder">
          <Eye className="preview-icon" />
          <span>预览不可用</span>
        </div>
      )
    }

    return (
      <div className="image-preview">
        <img
          src={`${apiBase}/preview/${filename}`}
          alt="处理结果"
          onError={() => setImageError(true)}
          onClick={() => handlePreview(filename)}
        />
      </div>
    )
  }

  if (loading) {
    return (
      <div className="batch-results">
        <div className="card">
          <div className="results-header">
            <h3>批处理结果</h3>
          </div>
          <div className="loading-state">
            <RefreshCw className="loading-icon spinning" />
            <p>正在获取结果...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="batch-results">
        <div className="card">
          <div className="results-header">
            <h3>批处理结果</h3>
            <button className="btn btn-secondary" onClick={onClose}>
              关闭
            </button>
          </div>
          <div className="error-state">
            <AlertCircle className="error-icon" />
            <p>{error}</p>
          </div>
        </div>
      </div>
    )
  }

  // 从localStorage获取任务元数据
  const getTaskMetadata = (taskId) => {
    const metadata = JSON.parse(localStorage.getItem('taskMetadata') || '{}')
    return metadata[taskId] || {}
  }

  // 按任务分组结果
  const groupedResults = results.reduce((groups, result) => {
    const metadata = getTaskMetadata(result.task_id)
    const taskName = metadata.task_name || result.task_name || `任务 ${Object.keys(groups).length + 1}`

    if (!groups[taskName]) {
      groups[taskName] = []
    }

    // 将元数据合并到结果中
    const enrichedResult = {
      ...result,
      task_name: taskName,
      original_files: metadata.original_files || result.original_files || []
    }

    groups[taskName].push(enrichedResult)
    return groups
  }, {})

  const successCount = results.filter(r => r.status === 'SUCCESS' || r.status === 'success').length
  const failureCount = results.filter(r => r.status === 'FAILURE' || r.status === 'failed' || r.status === 'error').length

  return (
    <div className="batch-results">
      <div className="card">
        <div className="results-header">
          <div className="results-title">
            <h3>批处理结果</h3>
            <div className="results-summary">
              <span className="success-count">成功: {successCount}</span>
              <span className="failure-count">失败: {failureCount}</span>
              <span className="total-count">总计: {results.length}</span>
            </div>
          </div>
          <div className="header-actions">
            {successCount > 0 && (
              <button className="btn btn-primary" onClick={handleDownloadAll}>
                <DownloadCloud className="btn-icon" />
                全部下载
              </button>
            )}
            <button className="btn btn-secondary" onClick={onClose}>
              关闭
            </button>
          </div>
        </div>

        <div className="results-list">
          {Object.entries(groupedResults).map(([taskName, taskResults]) => {
            const hasSuccess = taskResults.some(r => r.status === 'SUCCESS' || r.status === 'success')
            const hasError = taskResults.some(r => r.status === 'FAILURE' || r.status === 'failed' || r.status === 'error')
            const taskStatus = hasError ? 'error' : hasSuccess ? 'success' : 'pending'

            return (
              <div key={taskName} className={`task-group ${taskStatus}`}>
                <div className="task-header">
                  <div className="task-status">
                    {getStatusIcon(taskStatus)}
                    <div className="task-info">
                      <div className="task-name">{taskName}</div>
                      <div className="task-summary">
                        {taskResults.length} 个结果
                      </div>
                    </div>
                  </div>
                </div>

                <div className="task-results">
                  {taskResults.map((result, index) => (
                    <div key={result.task_id || index} className={`result-item ${getStatusClass(result.status)}`}>
                      {(result.status === 'SUCCESS' || result.status === 'success') && (result.result?.output_filename || result.output_filename) ? (
                        <div className="success-content">
                          <div className="result-preview">
                            <ImagePreview filename={result.result?.output_filename || result.output_filename} />
                          </div>

                          <div className="result-details">
                            <div className="file-info">
                              <div className="original-file">
                                原文件: <OriginalFilesInfo originalFiles={result.original_files} />
                              </div>
                              <div className="output-file">
                                输出文件: {result.result?.output_filename || result.output_filename || '未知'}
                              </div>
                              {(result.result?.processing_time || result.processing_time) && (
                                <div className="processing-time">
                                  处理时间: {(result.result?.processing_time || result.processing_time).toFixed(2)}秒
                                </div>
                              )}
                            </div>

                            <div className="result-actions">
                              <button
                                className="btn btn-primary"
                                onClick={() => handleDownload(result.result?.output_filename || result.output_filename)}
                              >
                                <Download className="btn-icon" />
                                下载
                              </button>
                            </div>
                          </div>
                        </div>
                      ) : (result.status === 'FAILURE' || result.status === 'failed' || result.status === 'error') ? (
                        <div className="error-content">
                          <div className="error-message">
                            {result.error || result.result?.error || '处理失败'}
                          </div>
                        </div>
                      ) : (
                        <div className="pending-content">
                          <div className="pending-message">
                            {loading ? '正在查询任务状态...' : '任务正在处理中，请稍候...'}
                          </div>
                          <div className="pending-note">
                            视频处理通常需要30-60秒，系统会自动更新状态
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>

        {results.length === 0 && (
          <div className="empty-results">
            <p>暂无结果</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default BatchResults
