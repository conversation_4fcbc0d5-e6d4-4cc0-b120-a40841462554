#include <QtQml/qqmlprivate.h>
#include <QtCore/qdir.h>
#include <QtCore/qurl.h>
#include <QtCore/qhash.h>
#include <QtCore/qstring.h>

namespace QmlCacheGeneratedCode {
namespace _0x5f_ImageVideoProcessor_main_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _0x5f_ImageVideoProcessor_components_FileSelectionArea_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _0x5f_ImageVideoProcessor_components_ParameterConfigArea_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _0x5f_ImageVideoProcessor_components_ProgressDisplayArea_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _0x5f_ImageVideoProcessor_components_ResultDisplayArea_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _0x5f_ImageVideoProcessor_components_TaskItem_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}
namespace _0x5f_ImageVideoProcessor_components_Icon_qml { 
    extern const unsigned char qmlData[];
    extern const QQmlPrivate::AOTCompiledFunction aotBuiltFunctions[];
    const QQmlPrivate::CachedQmlUnit unit = {
        reinterpret_cast<const QV4::CompiledData::Unit*>(&qmlData), &aotBuiltFunctions[0], nullptr
    };
}

}
namespace {
struct Registry {
    Registry();
    ~Registry();
    QHash<QString, const QQmlPrivate::CachedQmlUnit*> resourcePathToCachedUnit;
    static const QQmlPrivate::CachedQmlUnit *lookupCachedUnit(const QUrl &url);
};

Q_GLOBAL_STATIC(Registry, unitRegistry)


Registry::Registry() {
    resourcePathToCachedUnit.insert(QStringLiteral("/ImageVideoProcessor/main.qml"), &QmlCacheGeneratedCode::_0x5f_ImageVideoProcessor_main_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/ImageVideoProcessor/components/FileSelectionArea.qml"), &QmlCacheGeneratedCode::_0x5f_ImageVideoProcessor_components_FileSelectionArea_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/ImageVideoProcessor/components/ParameterConfigArea.qml"), &QmlCacheGeneratedCode::_0x5f_ImageVideoProcessor_components_ParameterConfigArea_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/ImageVideoProcessor/components/ProgressDisplayArea.qml"), &QmlCacheGeneratedCode::_0x5f_ImageVideoProcessor_components_ProgressDisplayArea_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/ImageVideoProcessor/components/ResultDisplayArea.qml"), &QmlCacheGeneratedCode::_0x5f_ImageVideoProcessor_components_ResultDisplayArea_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/ImageVideoProcessor/components/TaskItem.qml"), &QmlCacheGeneratedCode::_0x5f_ImageVideoProcessor_components_TaskItem_qml::unit);
    resourcePathToCachedUnit.insert(QStringLiteral("/ImageVideoProcessor/components/Icon.qml"), &QmlCacheGeneratedCode::_0x5f_ImageVideoProcessor_components_Icon_qml::unit);
    QQmlPrivate::RegisterQmlUnitCacheHook registration;
    registration.structVersion = 0;
    registration.lookupCachedQmlUnit = &lookupCachedUnit;
    QQmlPrivate::qmlregister(QQmlPrivate::QmlUnitCacheHookRegistration, &registration);
}

Registry::~Registry() {
    QQmlPrivate::qmlunregister(QQmlPrivate::QmlUnitCacheHookRegistration, quintptr(&lookupCachedUnit));
}

const QQmlPrivate::CachedQmlUnit *Registry::lookupCachedUnit(const QUrl &url) {
    if (url.scheme() != QLatin1String("qrc"))
        return nullptr;
    QString resourcePath = QDir::cleanPath(url.path());
    if (resourcePath.isEmpty())
        return nullptr;
    if (!resourcePath.startsWith(QLatin1Char('/')))
        resourcePath.prepend(QLatin1Char('/'));
    return unitRegistry()->resourcePathToCachedUnit.value(resourcePath, nullptr);
}
}
int QT_MANGLE_NAMESPACE(qInitResources_qmlcache_appImageVideoProcessor)() {
    ::unitRegistry();
    return 1;
}
Q_CONSTRUCTOR_FUNCTION(QT_MANGLE_NAMESPACE(qInitResources_qmlcache_appImageVideoProcessor))
int QT_MANGLE_NAMESPACE(qCleanupResources_qmlcache_appImageVideoProcessor)() {
    return 1;
}
