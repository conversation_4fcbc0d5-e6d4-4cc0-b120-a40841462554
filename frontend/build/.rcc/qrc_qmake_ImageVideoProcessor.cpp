/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.4.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // /home/<USER>/projects/homework/frontend/build/ImageVideoProcessor/qmldir
  0x0,0x0,0x1,0x89,
  0x6d,
  0x6f,0x64,0x75,0x6c,0x65,0x20,0x49,0x6d,0x61,0x67,0x65,0x56,0x69,0x64,0x65,0x6f,
  0x50,0x72,0x6f,0x63,0x65,0x73,0x73,0x6f,0x72,0xa,0x74,0x79,0x70,0x65,0x69,0x6e,
  0x66,0x6f,0x20,0x61,0x70,0x70,0x49,0x6d,0x61,0x67,0x65,0x56,0x69,0x64,0x65,0x6f,
  0x50,0x72,0x6f,0x63,0x65,0x73,0x73,0x6f,0x72,0x2e,0x71,0x6d,0x6c,0x74,0x79,0x70,
  0x65,0x73,0xa,0x70,0x72,0x65,0x66,0x65,0x72,0x20,0x3a,0x2f,0x49,0x6d,0x61,0x67,
  0x65,0x56,0x69,0x64,0x65,0x6f,0x50,0x72,0x6f,0x63,0x65,0x73,0x73,0x6f,0x72,0x2f,
  0xa,0x46,0x69,0x6c,0x65,0x53,0x65,0x6c,0x65,0x63,0x74,0x69,0x6f,0x6e,0x41,0x72,
  0x65,0x61,0x20,0x31,0x2e,0x30,0x20,0x63,0x6f,0x6d,0x70,0x6f,0x6e,0x65,0x6e,0x74,
  0x73,0x2f,0x46,0x69,0x6c,0x65,0x53,0x65,0x6c,0x65,0x63,0x74,0x69,0x6f,0x6e,0x41,
  0x72,0x65,0x61,0x2e,0x71,0x6d,0x6c,0xa,0x50,0x61,0x72,0x61,0x6d,0x65,0x74,0x65,
  0x72,0x43,0x6f,0x6e,0x66,0x69,0x67,0x41,0x72,0x65,0x61,0x20,0x31,0x2e,0x30,0x20,
  0x63,0x6f,0x6d,0x70,0x6f,0x6e,0x65,0x6e,0x74,0x73,0x2f,0x50,0x61,0x72,0x61,0x6d,
  0x65,0x74,0x65,0x72,0x43,0x6f,0x6e,0x66,0x69,0x67,0x41,0x72,0x65,0x61,0x2e,0x71,
  0x6d,0x6c,0xa,0x50,0x72,0x6f,0x67,0x72,0x65,0x73,0x73,0x44,0x69,0x73,0x70,0x6c,
  0x61,0x79,0x41,0x72,0x65,0x61,0x20,0x31,0x2e,0x30,0x20,0x63,0x6f,0x6d,0x70,0x6f,
  0x6e,0x65,0x6e,0x74,0x73,0x2f,0x50,0x72,0x6f,0x67,0x72,0x65,0x73,0x73,0x44,0x69,
  0x73,0x70,0x6c,0x61,0x79,0x41,0x72,0x65,0x61,0x2e,0x71,0x6d,0x6c,0xa,0x52,0x65,
  0x73,0x75,0x6c,0x74,0x44,0x69,0x73,0x70,0x6c,0x61,0x79,0x41,0x72,0x65,0x61,0x20,
  0x31,0x2e,0x30,0x20,0x63,0x6f,0x6d,0x70,0x6f,0x6e,0x65,0x6e,0x74,0x73,0x2f,0x52,
  0x65,0x73,0x75,0x6c,0x74,0x44,0x69,0x73,0x70,0x6c,0x61,0x79,0x41,0x72,0x65,0x61,
  0x2e,0x71,0x6d,0x6c,0xa,0x54,0x61,0x73,0x6b,0x49,0x74,0x65,0x6d,0x20,0x31,0x2e,
  0x30,0x20,0x63,0x6f,0x6d,0x70,0x6f,0x6e,0x65,0x6e,0x74,0x73,0x2f,0x54,0x61,0x73,
  0x6b,0x49,0x74,0x65,0x6d,0x2e,0x71,0x6d,0x6c,0xa,0x49,0x63,0x6f,0x6e,0x20,0x31,
  0x2e,0x30,0x20,0x63,0x6f,0x6d,0x70,0x6f,0x6e,0x65,0x6e,0x74,0x73,0x2f,0x49,0x63,
  0x6f,0x6e,0x2e,0x71,0x6d,0x6c,0xa,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // ImageVideoProcessor
  0x0,0x13,
  0xa,0x13,0x50,0xe2,
  0x0,0x49,
  0x0,0x6d,0x0,0x61,0x0,0x67,0x0,0x65,0x0,0x56,0x0,0x69,0x0,0x64,0x0,0x65,0x0,0x6f,0x0,0x50,0x0,0x72,0x0,0x6f,0x0,0x63,0x0,0x65,0x0,0x73,0x0,0x73,
  0x0,0x6f,0x0,0x72,
    // qmldir
  0x0,0x6,
  0x7,0x84,0x2b,0x2,
  0x0,0x71,
  0x0,0x6d,0x0,0x6c,0x0,0x64,0x0,0x69,0x0,0x72,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/ImageVideoProcessor
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/ImageVideoProcessor/qmldir
  0x0,0x0,0x0,0x2c,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0x5b,0x60,0x90,0x5a,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qmake_ImageVideoProcessor)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qmake_ImageVideoProcessor)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qmake_ImageVideoProcessor)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qmake_ImageVideoProcessor)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qmake_ImageVideoProcessor)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qmake_ImageVideoProcessor)(); }
   } dummy;
}
