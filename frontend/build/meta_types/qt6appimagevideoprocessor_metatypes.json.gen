[{"classes": [{"className": "NetworkManager", "methods": [{"access": "public", "arguments": [{"name": "filePath", "type": "QString"}], "name": "uploadFile", "returnType": "void"}, {"access": "public", "arguments": [{"name": "storedFilename", "type": "QString"}, {"name": "operation", "type": "QString"}, {"name": "parameters", "type": "QVariantMap"}], "name": "submitImageTask", "returnType": "void"}, {"access": "public", "arguments": [{"name": "storedFilename", "type": "QString"}, {"name": "operation", "type": "QString"}, {"name": "parameters", "type": "QVariantMap"}], "name": "submitVideoTask", "returnType": "void"}, {"access": "public", "arguments": [{"name": "taskId", "type": "QString"}], "name": "queryTaskStatus", "returnType": "void"}, {"access": "public", "arguments": [{"name": "taskId", "type": "QString"}], "name": "startStatusPolling", "returnType": "void"}, {"access": "public", "name": "stopStatusPolling", "returnType": "void"}, {"access": "public", "name": "checkConnection", "returnType": "void"}, {"access": "public", "name": "getAvailableOperations", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "baseUrl", "notify": "baseUrlChanged", "read": "baseUrl", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBaseUrl"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "isConnected", "notify": "isConnectedChanged", "read": "isConnected", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "NetworkManager", "signals": [{"access": "public", "name": "baseUrlChanged", "returnType": "void"}, {"access": "public", "name": "isConnectedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fileId", "type": "QString"}, {"name": "filePath", "type": "QString"}, {"name": "storedFilename", "type": "QString"}], "name": "fileUploaded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QString"}], "name": "fileUploadFailed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "taskId", "type": "QString"}], "name": "taskSubmitted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QString"}], "name": "taskSubmissionFailed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "taskId", "type": "QString"}, {"name": "progress", "type": "int"}, {"name": "status", "type": "QString"}], "name": "taskProgressUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "taskId", "type": "QString"}, {"name": "result", "type": "QVariantMap"}], "name": "taskCompleted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "taskId", "type": "QString"}, {"name": "error", "type": "QString"}], "name": "taskFailed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "connected", "type": "bool"}], "name": "connectionStatusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "operations", "type": "QVariantMap"}], "name": "operationsReceived", "returnType": "void"}], "slots": [{"access": "private", "name": "handleNetworkReply", "returnType": "void"}, {"access": "private", "name": "pollTaskStatus", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "networkmanager.h", "outputRevision": 68}]