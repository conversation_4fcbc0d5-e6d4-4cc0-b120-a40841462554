/****************************************************************************
** Meta object code from reading C++ file 'networkmanager.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.4.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../networkmanager.h"
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'networkmanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.4.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
namespace {
struct qt_meta_stringdata_NetworkManager_t {
    uint offsetsAndSizes[74];
    char stringdata0[15];
    char stringdata1[15];
    char stringdata2[1];
    char stringdata3[19];
    char stringdata4[13];
    char stringdata5[7];
    char stringdata6[9];
    char stringdata7[15];
    char stringdata8[17];
    char stringdata9[6];
    char stringdata10[14];
    char stringdata11[7];
    char stringdata12[21];
    char stringdata13[20];
    char stringdata14[9];
    char stringdata15[7];
    char stringdata16[14];
    char stringdata17[7];
    char stringdata18[11];
    char stringdata19[24];
    char stringdata20[10];
    char stringdata21[19];
    char stringdata22[11];
    char stringdata23[19];
    char stringdata24[15];
    char stringdata25[11];
    char stringdata26[16];
    char stringdata27[10];
    char stringdata28[11];
    char stringdata29[16];
    char stringdata30[16];
    char stringdata31[19];
    char stringdata32[18];
    char stringdata33[16];
    char stringdata34[23];
    char stringdata35[8];
    char stringdata36[12];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_NetworkManager_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_NetworkManager_t qt_meta_stringdata_NetworkManager = {
    {
        QT_MOC_LITERAL(0, 14),  // "NetworkManager"
        QT_MOC_LITERAL(15, 14),  // "baseUrlChanged"
        QT_MOC_LITERAL(30, 0),  // ""
        QT_MOC_LITERAL(31, 18),  // "isConnectedChanged"
        QT_MOC_LITERAL(50, 12),  // "fileUploaded"
        QT_MOC_LITERAL(63, 6),  // "fileId"
        QT_MOC_LITERAL(70, 8),  // "filePath"
        QT_MOC_LITERAL(79, 14),  // "storedFilename"
        QT_MOC_LITERAL(94, 16),  // "fileUploadFailed"
        QT_MOC_LITERAL(111, 5),  // "error"
        QT_MOC_LITERAL(117, 13),  // "taskSubmitted"
        QT_MOC_LITERAL(131, 6),  // "taskId"
        QT_MOC_LITERAL(138, 20),  // "taskSubmissionFailed"
        QT_MOC_LITERAL(159, 19),  // "taskProgressUpdated"
        QT_MOC_LITERAL(179, 8),  // "progress"
        QT_MOC_LITERAL(188, 6),  // "status"
        QT_MOC_LITERAL(195, 13),  // "taskCompleted"
        QT_MOC_LITERAL(209, 6),  // "result"
        QT_MOC_LITERAL(216, 10),  // "taskFailed"
        QT_MOC_LITERAL(227, 23),  // "connectionStatusChanged"
        QT_MOC_LITERAL(251, 9),  // "connected"
        QT_MOC_LITERAL(261, 18),  // "operationsReceived"
        QT_MOC_LITERAL(280, 10),  // "operations"
        QT_MOC_LITERAL(291, 18),  // "handleNetworkReply"
        QT_MOC_LITERAL(310, 14),  // "pollTaskStatus"
        QT_MOC_LITERAL(325, 10),  // "uploadFile"
        QT_MOC_LITERAL(336, 15),  // "submitImageTask"
        QT_MOC_LITERAL(352, 9),  // "operation"
        QT_MOC_LITERAL(362, 10),  // "parameters"
        QT_MOC_LITERAL(373, 15),  // "submitVideoTask"
        QT_MOC_LITERAL(389, 15),  // "queryTaskStatus"
        QT_MOC_LITERAL(405, 18),  // "startStatusPolling"
        QT_MOC_LITERAL(424, 17),  // "stopStatusPolling"
        QT_MOC_LITERAL(442, 15),  // "checkConnection"
        QT_MOC_LITERAL(458, 22),  // "getAvailableOperations"
        QT_MOC_LITERAL(481, 7),  // "baseUrl"
        QT_MOC_LITERAL(489, 11)   // "isConnected"
    },
    "NetworkManager",
    "baseUrlChanged",
    "",
    "isConnectedChanged",
    "fileUploaded",
    "fileId",
    "filePath",
    "storedFilename",
    "fileUploadFailed",
    "error",
    "taskSubmitted",
    "taskId",
    "taskSubmissionFailed",
    "taskProgressUpdated",
    "progress",
    "status",
    "taskCompleted",
    "result",
    "taskFailed",
    "connectionStatusChanged",
    "connected",
    "operationsReceived",
    "operations",
    "handleNetworkReply",
    "pollTaskStatus",
    "uploadFile",
    "submitImageTask",
    "operation",
    "parameters",
    "submitVideoTask",
    "queryTaskStatus",
    "startStatusPolling",
    "stopStatusPolling",
    "checkConnection",
    "getAvailableOperations",
    "baseUrl",
    "isConnected"
};
#undef QT_MOC_LITERAL
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_NetworkManager[] = {

 // content:
      10,       // revision
       0,       // classname
       0,    0, // classinfo
      21,   14, // methods
       2,  209, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      11,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  140,    2, 0x06,    3 /* Public */,
       3,    0,  141,    2, 0x06,    4 /* Public */,
       4,    3,  142,    2, 0x06,    5 /* Public */,
       8,    1,  149,    2, 0x06,    9 /* Public */,
      10,    1,  152,    2, 0x06,   11 /* Public */,
      12,    1,  155,    2, 0x06,   13 /* Public */,
      13,    3,  158,    2, 0x06,   15 /* Public */,
      16,    2,  165,    2, 0x06,   19 /* Public */,
      18,    2,  170,    2, 0x06,   22 /* Public */,
      19,    1,  175,    2, 0x06,   25 /* Public */,
      21,    1,  178,    2, 0x06,   27 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      23,    0,  181,    2, 0x08,   29 /* Private */,
      24,    0,  182,    2, 0x08,   30 /* Private */,

 // methods: name, argc, parameters, tag, flags, initial metatype offsets
      25,    1,  183,    2, 0x02,   31 /* Public */,
      26,    3,  186,    2, 0x02,   33 /* Public */,
      29,    3,  193,    2, 0x02,   37 /* Public */,
      30,    1,  200,    2, 0x02,   41 /* Public */,
      31,    1,  203,    2, 0x02,   43 /* Public */,
      32,    0,  206,    2, 0x02,   45 /* Public */,
      33,    0,  207,    2, 0x02,   46 /* Public */,
      34,    0,  208,    2, 0x02,   47 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QString,    5,    6,    7,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::QString,   11,   14,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::QVariantMap,   11,   17,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   11,    9,
    QMetaType::Void, QMetaType::Bool,   20,
    QMetaType::Void, QMetaType::QVariantMap,   22,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,

 // methods: parameters
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QVariantMap,    7,   27,   28,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QVariantMap,    7,   27,   28,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void, QMetaType::QString,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // properties: name, type, flags
      35, QMetaType::QString, 0x00015103, uint(0), 0,
      36, QMetaType::Bool, 0x00015001, uint(1), 0,

       0        // eod
};

Q_CONSTINIT const QMetaObject NetworkManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_NetworkManager.offsetsAndSizes,
    qt_meta_data_NetworkManager,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_NetworkManager_t,
        // property 'baseUrl'
        QtPrivate::TypeAndForceComplete<QString, std::true_type>,
        // property 'isConnected'
        QtPrivate::TypeAndForceComplete<bool, std::true_type>,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<NetworkManager, std::true_type>,
        // method 'baseUrlChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'isConnectedChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'fileUploaded'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'fileUploadFailed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'taskSubmitted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'taskSubmissionFailed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'taskProgressUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'taskCompleted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariantMap &, std::false_type>,
        // method 'taskFailed'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'connectionStatusChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        // method 'operationsReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariantMap &, std::false_type>,
        // method 'handleNetworkReply'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'pollTaskStatus'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'uploadFile'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'submitImageTask'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariantMap &, std::false_type>,
        // method 'submitVideoTask'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QVariantMap &, std::false_type>,
        // method 'queryTaskStatus'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'startStatusPolling'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'stopStatusPolling'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'checkConnection'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'getAvailableOperations'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void NetworkManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<NetworkManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->baseUrlChanged(); break;
        case 1: _t->isConnectedChanged(); break;
        case 2: _t->fileUploaded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 3: _t->fileUploadFailed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->taskSubmitted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->taskSubmissionFailed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->taskProgressUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 7: _t->taskCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 8: _t->taskFailed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 9: _t->connectionStatusChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 10: _t->operationsReceived((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1]))); break;
        case 11: _t->handleNetworkReply(); break;
        case 12: _t->pollTaskStatus(); break;
        case 13: _t->uploadFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 14: _t->submitImageTask((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[3]))); break;
        case 15: _t->submitVideoTask((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[3]))); break;
        case 16: _t->queryTaskStatus((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 17: _t->startStatusPolling((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 18: _t->stopStatusPolling(); break;
        case 19: _t->checkConnection(); break;
        case 20: _t->getAvailableOperations(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (NetworkManager::*)();
            if (_t _q_method = &NetworkManager::baseUrlChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)();
            if (_t _q_method = &NetworkManager::isConnectedChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QString & , const QString & , const QString & );
            if (_t _q_method = &NetworkManager::fileUploaded; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QString & );
            if (_t _q_method = &NetworkManager::fileUploadFailed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QString & );
            if (_t _q_method = &NetworkManager::taskSubmitted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QString & );
            if (_t _q_method = &NetworkManager::taskSubmissionFailed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QString & , int , const QString & );
            if (_t _q_method = &NetworkManager::taskProgressUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QString & , const QVariantMap & );
            if (_t _q_method = &NetworkManager::taskCompleted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QString & , const QString & );
            if (_t _q_method = &NetworkManager::taskFailed; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(bool );
            if (_t _q_method = &NetworkManager::connectionStatusChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (NetworkManager::*)(const QVariantMap & );
            if (_t _q_method = &NetworkManager::operationsReceived; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 10;
                return;
            }
        }
    }else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<NetworkManager *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast< QString*>(_v) = _t->baseUrl(); break;
        case 1: *reinterpret_cast< bool*>(_v) = _t->isConnected(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<NetworkManager *>(_o);
        (void)_t;
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setBaseUrl(*reinterpret_cast< QString*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    } else if (_c == QMetaObject::BindableProperty) {
    }
}

const QMetaObject *NetworkManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *NetworkManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_NetworkManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int NetworkManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 21)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 21;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 21)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 21;
    }else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void NetworkManager::baseUrlChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void NetworkManager::isConnectedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void NetworkManager::fileUploaded(const QString & _t1, const QString & _t2, const QString & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void NetworkManager::fileUploadFailed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void NetworkManager::taskSubmitted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void NetworkManager::taskSubmissionFailed(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void NetworkManager::taskProgressUpdated(const QString & _t1, int _t2, const QString & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void NetworkManager::taskCompleted(const QString & _t1, const QVariantMap & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void NetworkManager::taskFailed(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void NetworkManager::connectionStatusChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}

// SIGNAL 10
void NetworkManager::operationsReceived(const QVariantMap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 10, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
