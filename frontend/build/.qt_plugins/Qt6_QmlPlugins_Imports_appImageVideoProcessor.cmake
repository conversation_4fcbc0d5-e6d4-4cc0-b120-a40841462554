set(qml_import_scanner_imports_count 21)
set(qml_import_scanner_import_0 "CLASSNAME;QtQuick2Plugin;LINKTARGET;Qt6::qtquick2plugin;NAME;QtQuick;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick;PLUGIN;qtquick2plugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/;RELATIVEPATH;QtQuick;TYPE;module;")
set(qml_import_scanner_import_1 "NAME;QtQml;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQml;RELATIVEPATH;QtQml;TYPE;module;")
set(qml_import_scanner_import_2 "CLASSNAME;QtQuickControls2Plugin;LINKTARGET;Qt6::qtquickcontrols2plugin;NAME;QtQuick.Controls;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls;PLUGIN;qtquickcontrols2plugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/;RELATIVEPATH;QtQuick/Controls;TYPE;module;")
set(qml_import_scanner_import_3 "CLASSNAME;QtQuickControls2ImplPlugin;LINKTARGET;Qt6::qtquickcontrols2implplugin;NAME;QtQuick.Controls.impl;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/impl;PLUGIN;qtquickcontrols2implplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/impl/;RELATIVEPATH;QtQuick/Controls/impl;TYPE;module;")
set(qml_import_scanner_import_4 "CLASSNAME;QtQuickControls2FusionStylePlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ApplicationWindow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/BusyIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Button.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/CheckBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/CheckDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ComboBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/DelayButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Dial.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Dialog.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/DialogButtonBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Drawer.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Frame.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/GroupBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/HorizontalHeaderView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ItemDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Label.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Menu.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/MenuBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/MenuBarItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/MenuItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/MenuSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Page.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/PageIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Pane.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Popup.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ProgressBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/RadioButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/RadioDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/RangeSlider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/RoundButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ScrollBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ScrollIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ScrollView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/SelectionRectangle.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Slider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/SpinBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/SplitView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/SwipeDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Switch.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/SwitchDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/TabBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/TabButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/TextArea.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/TextField.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ToolBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ToolButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ToolSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/ToolTip.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/TreeViewDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/Tumbler.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleplugin;NAME;QtQuick.Controls.Fusion;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion;PLUGIN;qtquickcontrols2fusionstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/;RELATIVEPATH;QtQuick/Controls/Fusion;TYPE;module;")
set(qml_import_scanner_import_5 "CLASSNAME;QtQuickControls2MaterialStylePlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ApplicationWindow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/BusyIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Button.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/CheckBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/CheckDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ComboBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/DelayButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Dial.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Dialog.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/DialogButtonBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Drawer.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Frame.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/GroupBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/HorizontalHeaderView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ItemDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Label.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Menu.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/MenuBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/MenuBarItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/MenuItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/MenuSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Page.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/PageIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Pane.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Popup.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ProgressBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/RadioButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/RadioDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/RangeSlider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/RoundButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ScrollBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ScrollIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ScrollView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/SelectionRectangle.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Slider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/SpinBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/SplitView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/StackView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/SwipeDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/SwipeView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Switch.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/SwitchDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/TabBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/TabButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/TextArea.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/TextField.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ToolBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ToolButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ToolSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/ToolTip.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/TreeViewDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/Tumbler.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleplugin;NAME;QtQuick.Controls.Material;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material;PLUGIN;qtquickcontrols2materialstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/;RELATIVEPATH;QtQuick/Controls/Material;TYPE;module;")
set(qml_import_scanner_import_6 "CLASSNAME;QtQuickControls2ImagineStylePlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ApplicationWindow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/BusyIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Button.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/CheckBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/CheckDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ComboBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/DelayButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Dial.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Dialog.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/DialogButtonBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Drawer.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Frame.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/GroupBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/HorizontalHeaderView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ItemDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Label.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Menu.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/MenuItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/MenuSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Page.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/PageIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Pane.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Popup.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ProgressBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/RadioButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/RadioDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/RangeSlider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/RoundButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ScrollBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ScrollIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ScrollView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/SelectionRectangle.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Slider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/SpinBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/SplitView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/StackView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/SwipeDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/SwipeView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Switch.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/SwitchDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/TabBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/TabButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/TextArea.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/TextField.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ToolBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ToolButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ToolSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/ToolTip.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/Tumbler.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleplugin;NAME;QtQuick.Controls.Imagine;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine;PLUGIN;qtquickcontrols2imaginestyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/;RELATIVEPATH;QtQuick/Controls/Imagine;TYPE;module;")
set(qml_import_scanner_import_7 "CLASSNAME;QtQuickControls2UniversalStylePlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ApplicationWindow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/BusyIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Button.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/CheckBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/CheckDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ComboBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/DelayButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Dial.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Dialog.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/DialogButtonBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Drawer.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Frame.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/GroupBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/HorizontalHeaderView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ItemDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Label.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Menu.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/MenuBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/MenuBarItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/MenuItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/MenuSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Page.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/PageIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Pane.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Popup.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ProgressBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/RadioButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/RadioDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/RangeSlider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/RoundButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ScrollBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ScrollIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ScrollView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/SelectionRectangle.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Slider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/SpinBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/SplitView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/StackView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/SwipeDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Switch.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/SwitchDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/TabBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/TabButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/TextArea.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/TextField.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ToolBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ToolButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ToolSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/ToolTip.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/Tumbler.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleplugin;NAME;QtQuick.Controls.Universal;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal;PLUGIN;qtquickcontrols2universalstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/;RELATIVEPATH;QtQuick/Controls/Universal;TYPE;module;")
set(qml_import_scanner_import_8 "NAME;QtQuick.Controls.Windows;TYPE;module;")
set(qml_import_scanner_import_9 "NAME;QtQuick.Controls.macOS;TYPE;module;")
set(qml_import_scanner_import_10 "NAME;QtQuick.Controls.iOS;TYPE;module;")
set(qml_import_scanner_import_11 "CLASSNAME;QtQuickControls2BasicStylePlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/AbstractButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Action.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ActionGroup.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ApplicationWindow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/BusyIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Button.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ButtonGroup.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Calendar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/CalendarModel.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/CheckBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/CheckDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ComboBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Container.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Control.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/DayOfWeekRow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/DelayButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Dial.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Dialog.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/DialogButtonBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Drawer.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Frame.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/GroupBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/HorizontalHeaderView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ItemDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Label.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Menu.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/MenuBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/MenuBarItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/MenuItem.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/MenuSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/MonthGrid.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Page.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/PageIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Pane.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Popup.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ProgressBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/RadioButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/RadioDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/RangeSlider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/RoundButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ScrollBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ScrollIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ScrollView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/SelectionRectangle.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Slider.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/SpinBox.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/SplitView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/StackView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/SwipeDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/SwipeView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Switch.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/SwitchDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/TabBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/TabButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/TextArea.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/TextField.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ToolBar.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ToolButton.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ToolSeparator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/ToolTip.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/TreeViewDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/Tumbler.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/VerticalHeaderView.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/WeekNumberColumn.qml;LINKTARGET;Qt6::qtquickcontrols2basicstyleplugin;NAME;QtQuick.Controls.Basic;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic;PLUGIN;qtquickcontrols2basicstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/;RELATIVEPATH;QtQuick/Controls/Basic;TYPE;module;")
set(qml_import_scanner_import_12 "CLASSNAME;QtQuickTemplates2Plugin;LINKTARGET;Qt6::qtquicktemplates2plugin;NAME;QtQuick.Templates;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Templates;PLUGIN;qtquicktemplates2plugin;PREFER;:/qt-project.org/imports/QtQuick/Templates/;RELATIVEPATH;QtQuick/Templates;TYPE;module;")
set(qml_import_scanner_import_13 "CLASSNAME;QtQuickControls2FusionStyleImplPlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/impl/ButtonPanel.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/impl/CheckIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/impl/RadioIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/impl/SliderGroove.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/impl/SliderHandle.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleimplplugin;NAME;QtQuick.Controls.Fusion.impl;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Fusion/impl;PLUGIN;qtquickcontrols2fusionstyleimplplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/impl/;RELATIVEPATH;QtQuick/Controls/Fusion/impl;TYPE;module;")
set(qml_import_scanner_import_14 "CLASSNAME;QtQuick_WindowPlugin;LINKTARGET;Qt6::quickwindow;NAME;QtQuick.Window;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Window;PLUGIN;quickwindowplugin;PREFER;:/qt-project.org/imports/QtQuick/Window/;RELATIVEPATH;QtQuick/Window;TYPE;module;")
set(qml_import_scanner_import_15 "CLASSNAME;QtQuickControls2MaterialStyleImplPlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/BoxShadow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/CheckIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/CursorDelegate.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/ElevationEffect.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/RadioIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/RectangularGlow.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/SliderHandle.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleimplplugin;NAME;QtQuick.Controls.Material.impl;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Material/impl;PLUGIN;qtquickcontrols2materialstyleimplplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/impl/;RELATIVEPATH;QtQuick/Controls/Material/impl;TYPE;module;")
set(qml_import_scanner_import_16 "CLASSNAME;QtQuickControls2ImagineStyleImplPlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/impl/OpacityMask.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleimplplugin;NAME;QtQuick.Controls.Imagine.impl;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Imagine/impl;PLUGIN;qtquickcontrols2imaginestyleimplplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/impl/;RELATIVEPATH;QtQuick/Controls/Imagine/impl;TYPE;module;")
set(qml_import_scanner_import_17 "CLASSNAME;QtQuickControls2UniversalStyleImplPlugin;COMPONENTS;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/impl/CheckIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/impl/RadioIndicator.qml;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleimplplugin;NAME;QtQuick.Controls.Universal.impl;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Universal/impl;PLUGIN;qtquickcontrols2universalstyleimplplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/impl/;RELATIVEPATH;QtQuick/Controls/Universal/impl;TYPE;module;")
set(qml_import_scanner_import_18 "CLASSNAME;QtQuickControls2BasicStyleImplPlugin;LINKTARGET;Qt6::qtquickcontrols2basicstyleimplplugin;NAME;QtQuick.Controls.Basic.impl;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Controls/Basic/impl;PLUGIN;qtquickcontrols2basicstyleimplplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/impl/;RELATIVEPATH;QtQuick/Controls/Basic/impl;TYPE;module;")
set(qml_import_scanner_import_19 "NAME;QtQuick.Shapes;TYPE;module;")
set(qml_import_scanner_import_20 "CLASSNAME;QtQuickLayoutsPlugin;LINKTARGET;Qt6::qquicklayoutsplugin;NAME;QtQuick.Layouts;PATH;/usr/lib/x86_64-linux-gnu/qt6/qml/QtQuick/Layouts;PLUGIN;qquicklayoutsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Layouts/;RELATIVEPATH;QtQuick/Layouts;TYPE;module;")

