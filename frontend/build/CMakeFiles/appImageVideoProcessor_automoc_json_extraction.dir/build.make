# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/projects/homework/frontend

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/projects/homework/frontend/build

# Utility rule file for appImageVideoProcessor_automoc_json_extraction.

# Include any custom commands dependencies for this target.
include CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/progress.make

CMakeFiles/appImageVideoProcessor_automoc_json_extraction: /usr/lib/qt6/libexec/cmake_automoc_parser
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/projects/homework/frontend/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running AUTOMOC file extraction for target appImageVideoProcessor"
	/usr/lib/qt6/libexec/cmake_automoc_parser --cmake-autogen-cache-file /home/<USER>/projects/homework/frontend/build/CMakeFiles/appImageVideoProcessor_autogen.dir/ParseCache.txt --cmake-autogen-info-file /home/<USER>/projects/homework/frontend/build/CMakeFiles/appImageVideoProcessor_autogen.dir/AutogenInfo.json --output-file-path /home/<USER>/projects/homework/frontend/build/meta_types/appImageVideoProcessor_json_file_list.txt --timestamp-file-path /home/<USER>/projects/homework/frontend/build/meta_types/appImageVideoProcessor_json_file_list.txt.timestamp --cmake-autogen-include-dir-path /home/<USER>/projects/homework/frontend/build/appImageVideoProcessor_autogen/include

appImageVideoProcessor_automoc_json_extraction: CMakeFiles/appImageVideoProcessor_automoc_json_extraction
appImageVideoProcessor_automoc_json_extraction: CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/build.make
.PHONY : appImageVideoProcessor_automoc_json_extraction

# Rule to build all files generated by this target.
CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/build: appImageVideoProcessor_automoc_json_extraction
.PHONY : CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/build

CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/cmake_clean.cmake
.PHONY : CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/clean

CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/depend:
	cd /home/<USER>/projects/homework/frontend/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/projects/homework/frontend /home/<USER>/projects/homework/frontend /home/<USER>/projects/homework/frontend/build /home/<USER>/projects/homework/frontend/build /home/<USER>/projects/homework/frontend/build/CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/appImageVideoProcessor_automoc_json_extraction.dir/depend

