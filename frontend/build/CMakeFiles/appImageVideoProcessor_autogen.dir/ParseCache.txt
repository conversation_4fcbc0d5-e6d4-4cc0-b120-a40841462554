# Generated by CMake. Changes will be overwritten.
/home/<USER>/projects/homework/frontend/networkmanager.cpp
/home/<USER>/projects/homework/frontend/build/.rcc/qmlcache/appImageVideoProcessor_qmlcache_loader.cpp
/home/<USER>/projects/homework/frontend/main.cpp
/home/<USER>/projects/homework/frontend/networkmanager.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/projects/homework/frontend/build/appImageVideoProcessor_autogen/moc_predefs.h
 mdp:/home/<USER>/projects/homework/frontend/networkmanager.h
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/13/algorithm
 mdp:/usr/include/c++/13/array
 mdp:/usr/include/c++/13/atomic
 mdp:/usr/include/c++/13/backward/auto_ptr.h
 mdp:/usr/include/c++/13/backward/binders.h
 mdp:/usr/include/c++/13/bit
 mdp:/usr/include/c++/13/bits/algorithmfwd.h
 mdp:/usr/include/c++/13/bits/align.h
 mdp:/usr/include/c++/13/bits/alloc_traits.h
 mdp:/usr/include/c++/13/bits/allocated_ptr.h
 mdp:/usr/include/c++/13/bits/allocator.h
 mdp:/usr/include/c++/13/bits/atomic_base.h
 mdp:/usr/include/c++/13/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/13/bits/atomic_wait.h
 mdp:/usr/include/c++/13/bits/basic_ios.h
 mdp:/usr/include/c++/13/bits/basic_ios.tcc
 mdp:/usr/include/c++/13/bits/basic_string.h
 mdp:/usr/include/c++/13/bits/basic_string.tcc
 mdp:/usr/include/c++/13/bits/c++0x_warning.h
 mdp:/usr/include/c++/13/bits/char_traits.h
 mdp:/usr/include/c++/13/bits/charconv.h
 mdp:/usr/include/c++/13/bits/concept_check.h
 mdp:/usr/include/c++/13/bits/cpp_type_traits.h
 mdp:/usr/include/c++/13/bits/cxxabi_forced.h
 mdp:/usr/include/c++/13/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/13/bits/enable_special_members.h
 mdp:/usr/include/c++/13/bits/erase_if.h
 mdp:/usr/include/c++/13/bits/exception.h
 mdp:/usr/include/c++/13/bits/exception_defines.h
 mdp:/usr/include/c++/13/bits/exception_ptr.h
 mdp:/usr/include/c++/13/bits/functexcept.h
 mdp:/usr/include/c++/13/bits/functional_hash.h
 mdp:/usr/include/c++/13/bits/hash_bytes.h
 mdp:/usr/include/c++/13/bits/invoke.h
 mdp:/usr/include/c++/13/bits/ios_base.h
 mdp:/usr/include/c++/13/bits/iterator_concepts.h
 mdp:/usr/include/c++/13/bits/list.tcc
 mdp:/usr/include/c++/13/bits/locale_classes.h
 mdp:/usr/include/c++/13/bits/locale_classes.tcc
 mdp:/usr/include/c++/13/bits/locale_facets.h
 mdp:/usr/include/c++/13/bits/locale_facets.tcc
 mdp:/usr/include/c++/13/bits/localefwd.h
 mdp:/usr/include/c++/13/bits/max_size_type.h
 mdp:/usr/include/c++/13/bits/memory_resource.h
 mdp:/usr/include/c++/13/bits/memoryfwd.h
 mdp:/usr/include/c++/13/bits/mofunc_impl.h
 mdp:/usr/include/c++/13/bits/move.h
 mdp:/usr/include/c++/13/bits/move_only_function.h
 mdp:/usr/include/c++/13/bits/nested_exception.h
 mdp:/usr/include/c++/13/bits/new_allocator.h
 mdp:/usr/include/c++/13/bits/node_handle.h
 mdp:/usr/include/c++/13/bits/ostream.tcc
 mdp:/usr/include/c++/13/bits/ostream_insert.h
 mdp:/usr/include/c++/13/bits/parse_numbers.h
 mdp:/usr/include/c++/13/bits/postypes.h
 mdp:/usr/include/c++/13/bits/predefined_ops.h
 mdp:/usr/include/c++/13/bits/ptr_traits.h
 mdp:/usr/include/c++/13/bits/range_access.h
 mdp:/usr/include/c++/13/bits/ranges_algo.h
 mdp:/usr/include/c++/13/bits/ranges_algobase.h
 mdp:/usr/include/c++/13/bits/ranges_base.h
 mdp:/usr/include/c++/13/bits/ranges_cmp.h
 mdp:/usr/include/c++/13/bits/ranges_uninitialized.h
 mdp:/usr/include/c++/13/bits/ranges_util.h
 mdp:/usr/include/c++/13/bits/refwrap.h
 mdp:/usr/include/c++/13/bits/requires_hosted.h
 mdp:/usr/include/c++/13/bits/shared_ptr.h
 mdp:/usr/include/c++/13/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/13/bits/shared_ptr_base.h
 mdp:/usr/include/c++/13/bits/specfun.h
 mdp:/usr/include/c++/13/bits/std_abs.h
 mdp:/usr/include/c++/13/bits/std_function.h
 mdp:/usr/include/c++/13/bits/std_mutex.h
 mdp:/usr/include/c++/13/bits/stl_algo.h
 mdp:/usr/include/c++/13/bits/stl_algobase.h
 mdp:/usr/include/c++/13/bits/stl_bvector.h
 mdp:/usr/include/c++/13/bits/stl_construct.h
 mdp:/usr/include/c++/13/bits/stl_function.h
 mdp:/usr/include/c++/13/bits/stl_heap.h
 mdp:/usr/include/c++/13/bits/stl_iterator.h
 mdp:/usr/include/c++/13/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/13/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/13/bits/stl_list.h
 mdp:/usr/include/c++/13/bits/stl_map.h
 mdp:/usr/include/c++/13/bits/stl_multimap.h
 mdp:/usr/include/c++/13/bits/stl_numeric.h
 mdp:/usr/include/c++/13/bits/stl_pair.h
 mdp:/usr/include/c++/13/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/13/bits/stl_relops.h
 mdp:/usr/include/c++/13/bits/stl_tempbuf.h
 mdp:/usr/include/c++/13/bits/stl_tree.h
 mdp:/usr/include/c++/13/bits/stl_uninitialized.h
 mdp:/usr/include/c++/13/bits/stl_vector.h
 mdp:/usr/include/c++/13/bits/stream_iterator.h
 mdp:/usr/include/c++/13/bits/streambuf.tcc
 mdp:/usr/include/c++/13/bits/streambuf_iterator.h
 mdp:/usr/include/c++/13/bits/string_view.tcc
 mdp:/usr/include/c++/13/bits/stringfwd.h
 mdp:/usr/include/c++/13/bits/uniform_int_dist.h
 mdp:/usr/include/c++/13/bits/unique_ptr.h
 mdp:/usr/include/c++/13/bits/uses_allocator.h
 mdp:/usr/include/c++/13/bits/uses_allocator_args.h
 mdp:/usr/include/c++/13/bits/utility.h
 mdp:/usr/include/c++/13/bits/vector.tcc
 mdp:/usr/include/c++/13/cctype
 mdp:/usr/include/c++/13/cerrno
 mdp:/usr/include/c++/13/chrono
 mdp:/usr/include/c++/13/climits
 mdp:/usr/include/c++/13/clocale
 mdp:/usr/include/c++/13/cmath
 mdp:/usr/include/c++/13/compare
 mdp:/usr/include/c++/13/concepts
 mdp:/usr/include/c++/13/cstddef
 mdp:/usr/include/c++/13/cstdint
 mdp:/usr/include/c++/13/cstdlib
 mdp:/usr/include/c++/13/cstring
 mdp:/usr/include/c++/13/cwchar
 mdp:/usr/include/c++/13/cwctype
 mdp:/usr/include/c++/13/debug/assertions.h
 mdp:/usr/include/c++/13/debug/debug.h
 mdp:/usr/include/c++/13/exception
 mdp:/usr/include/c++/13/ext/aligned_buffer.h
 mdp:/usr/include/c++/13/ext/alloc_traits.h
 mdp:/usr/include/c++/13/ext/atomicity.h
 mdp:/usr/include/c++/13/ext/concurrence.h
 mdp:/usr/include/c++/13/ext/numeric_traits.h
 mdp:/usr/include/c++/13/ext/string_conversions.h
 mdp:/usr/include/c++/13/ext/type_traits.h
 mdp:/usr/include/c++/13/functional
 mdp:/usr/include/c++/13/initializer_list
 mdp:/usr/include/c++/13/ios
 mdp:/usr/include/c++/13/iosfwd
 mdp:/usr/include/c++/13/iterator
 mdp:/usr/include/c++/13/limits
 mdp:/usr/include/c++/13/list
 mdp:/usr/include/c++/13/map
 mdp:/usr/include/c++/13/memory
 mdp:/usr/include/c++/13/new
 mdp:/usr/include/c++/13/numbers
 mdp:/usr/include/c++/13/numeric
 mdp:/usr/include/c++/13/optional
 mdp:/usr/include/c++/13/ostream
 mdp:/usr/include/c++/13/pstl/execution_defs.h
 mdp:/usr/include/c++/13/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/13/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/13/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/13/stdexcept
 mdp:/usr/include/c++/13/streambuf
 mdp:/usr/include/c++/13/string
 mdp:/usr/include/c++/13/string_view
 mdp:/usr/include/c++/13/tr1/bessel_function.tcc
 mdp:/usr/include/c++/13/tr1/beta_function.tcc
 mdp:/usr/include/c++/13/tr1/ell_integral.tcc
 mdp:/usr/include/c++/13/tr1/exp_integral.tcc
 mdp:/usr/include/c++/13/tr1/gamma.tcc
 mdp:/usr/include/c++/13/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/13/tr1/legendre_function.tcc
 mdp:/usr/include/c++/13/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/13/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/13/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/13/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/13/tr1/special_function_util.h
 mdp:/usr/include/c++/13/tuple
 mdp:/usr/include/c++/13/type_traits
 mdp:/usr/include/c++/13/typeinfo
 mdp:/usr/include/c++/13/unordered_map
 mdp:/usr/include/c++/13/utility
 mdp:/usr/include/c++/13/variant
 mdp:/usr/include/c++/13/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/syscall.h
 mdp:/usr/include/time.h
 mdp:/usr/include/unistd.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/wctype.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/asm/unistd.h
 mdp:/usr/include/x86_64-linux-gnu/asm/unistd_64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/confname.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/environments.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_core.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_posix.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/local_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix_opt.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-least.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/syscall.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/unistd_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QFlags
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QIODevice
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QList
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMap
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QMetaType
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QObject
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QSharedDataPointer
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QString
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QStringList
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QTimer
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QUrl
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QVariant
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/QVariantMap
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qanystringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydataops.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qarraydatapointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qatomic_cxx11.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasicatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbasictimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbindingstorage.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearraylist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qbytearrayview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcalendar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qchar.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompare_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcompilerdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qconfig.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainerinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontainertools_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcontiguouscache.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qcryptographichash.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatastream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdatetime.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qdebug.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qflags.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qforeach.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qgenericatomic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobal.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qglobalstatic.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhash.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qhashfunctions.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevice.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiodevicebase.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterable.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qiterator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlocale.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qlogging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmath.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetacontainer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qmetatype.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnamespace.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qnumeric.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobject_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qobjectdefs_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qpair.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qprocessordetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qrefcount.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qscopeguard.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qset.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qshareddata_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsharedpointer_impl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstring.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringalgorithms.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringconverter_base.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringfwd.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringlist.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringliteral.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringmatcher.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringtokenizer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qstringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsysinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qsystemdetection.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtaggedpointer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtconfigmacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcore-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtcoreexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtextstream.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtimer.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtmetamacros.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qtypeinfo.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qurl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qutf8stringview.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvariant.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvariantmap.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qvarlengtharray.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtCore/qversiontagging.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/QNetworkAccessManager
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/QNetworkReply
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/QNetworkRequest
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/QSslConfiguration
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/QSslPreSharedKeyAuthenticator
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qabstractsocket.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qhostaddress.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qnetworkaccessmanager.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qnetworkreply.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qnetworkrequest.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qssl.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qsslcertificate.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qsslconfiguration.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qsslerror.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qsslpresharedkeyauthenticator.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qsslsocket.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qtcpsocket.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qtnetwork-config.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qtnetworkexports.h
 mdp:/usr/include/x86_64-linux-gnu/qt6/QtNetwork/qtnetworkglobal.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/syscall.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/13/include/stdbool.h
 mdp:/usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h
