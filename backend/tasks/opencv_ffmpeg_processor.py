"""
简化的OpenCV + FFmpeg视频处理器
去除GPU支持，专注于CPU处理和FFmpeg音频合并
"""

import cv2
import subprocess
import os
import time
import tempfile
import json
from typing import Callable, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class OpenCVFFmpegProcessor:
    """
    简化的OpenCV + FFmpeg视频处理器
    - OpenCV处理视频帧（CPU）
    - FFmpeg处理音频合并（专业）
    - 完全去除MoviePy和GPU依赖
    """
    
    def __init__(self, input_path: str, preserve_audio: bool = True):
        self.input_path = input_path
        self.preserve_audio = preserve_audio
        self.output_dir = 'output'
        os.makedirs(self.output_dir, exist_ok=True)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
    
    def get_video_info(self) -> Dict[str, Any]:
        """使用FFprobe获取视频信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json', 
                '-show_format', '-show_streams', self.input_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)
            
            # 解析视频流信息
            video_stream = None
            audio_stream = None
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video' and video_stream is None:
                    video_stream = stream
                elif stream['codec_type'] == 'audio' and audio_stream is None:
                    audio_stream = stream
            
            if not video_stream:
                raise ValueError("No video stream found")
            
            duration = float(info['format']['duration'])
            fps = eval(video_stream['r_frame_rate'])  # 例如 "60/1"
            width = int(video_stream['width'])
            height = int(video_stream['height'])
            total_frames = int(duration * fps)
            
            return {
                'duration': duration,
                'fps': fps,
                'size': (width, height),
                'total_frames': total_frames,
                'has_audio': audio_stream is not None
            }
            
        except Exception as e:
            logger.error(f"Failed to get video info: {e}")
            raise
    
    def process_frames_with_opencv(
        self,
        opencv_processor: Callable[[any], any],
        output_prefix: str,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
        batch_size: int = 30
    ) -> Dict[str, Any]:
        """
        使用OpenCV处理视频帧（纯CPU实现）
        """
        video_info = self.get_video_info()
        
        # 创建临时视频文件（无音频）
        temp_video_path = os.path.join(
            tempfile.gettempdir(), 
            f'temp_processed_{int(time.time())}.mp4'
        )
        
        if progress_callback:
            progress_callback(5, 100, 'Starting video processing...')
        
        # 使用OpenCV进行处理
        cap = cv2.VideoCapture(self.input_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {self.input_path}")
        
        # 获取视频属性
        fps = video_info['fps']
        width, height = video_info['size']
        total_frames = video_info['total_frames']
        
        # 先处理第一帧来确定输出尺寸
        ret, first_frame = cap.read()
        if not ret:
            cap.release()
            raise ValueError("Cannot read first frame from video")

        # 处理第一帧以确定输出尺寸
        processed_first_frame = opencv_processor(first_frame)

        # 确定输出尺寸
        if len(processed_first_frame.shape) == 3:
            # 彩色图像
            output_height, output_width = processed_first_frame.shape[:2]
        else:
            # 灰度图像，需要转换为3通道
            output_height, output_width = processed_first_frame.shape
            processed_first_frame = cv2.cvtColor(processed_first_frame, cv2.COLOR_GRAY2BGR)

        # 创建视频写入器，使用处理后的尺寸
        # 尝试使用H.264编码器（更兼容）
        fourcc = cv2.VideoWriter_fourcc(*'H264')
        out = cv2.VideoWriter(temp_video_path, fourcc, fps, (output_width, output_height))

        # 如果H264失败，尝试使用mp4v
        if not out.isOpened():
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(temp_video_path, fourcc, fps, (output_width, output_height))

        if not out.isOpened():
            cap.release()
            raise ValueError("Cannot create output video writer")

        try:
            # 写入第一帧
            out.write(processed_first_frame)
            processed_frames = 1
            frame_batch = []

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_batch.append(frame)

                # 批量处理
                if len(frame_batch) >= batch_size:
                    processed_batch = self._process_frame_batch(
                        frame_batch, opencv_processor
                    )
                    for processed_frame in processed_batch:
                        # 确保处理后的帧是3通道的
                        if len(processed_frame.shape) == 2:
                            processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
                        out.write(processed_frame)

                    processed_frames += len(processed_batch)
                    frame_batch = []

                    # 更新进度
                    if progress_callback:
                        progress = 10 + int((processed_frames / total_frames) * 70)
                        progress_callback(progress, 100, f'Processed {processed_frames}/{total_frames} frames')
            
            # 处理剩余帧
            if frame_batch:
                processed_batch = self._process_frame_batch(
                    frame_batch, opencv_processor
                )
                for processed_frame in processed_batch:
                    # 确保处理后的帧是3通道的
                    if len(processed_frame.shape) == 2:
                        processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
                    out.write(processed_frame)
                processed_frames += len(processed_batch)
            
        finally:
            cap.release()
            out.release()
        
        if progress_callback:
            progress_callback(85, 100, 'Merging audio with FFmpeg...')
        
        # 创建最终输出文件
        output_filename = f"{output_prefix}_{int(time.time())}.mp4"
        output_path = os.path.join(self.output_dir, output_filename)
        
        # 使用FFmpeg合并音频
        if self.preserve_audio and video_info['has_audio']:
            self._merge_audio_with_ffmpeg(temp_video_path, output_path)
        else:
            # 不需要音频，直接移动文件
            import shutil
            shutil.move(temp_video_path, output_path)
        
        # 清理临时文件
        if os.path.exists(temp_video_path):
            os.remove(temp_video_path)
        
        if progress_callback:
            progress_callback(100, 100, 'Processing completed')
        
        return {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_path': self.input_path,
            'has_audio': video_info['has_audio'] and self.preserve_audio,
            'duration': video_info['duration'],
            'fps': video_info['fps'],
            'size': video_info['size'],
            'frames_processed': processed_frames,
            'optimization': 'opencv_ffmpeg_cpu'
        }
    
    def _process_frame_batch(
        self,
        frame_batch: list,
        opencv_processor: Callable
    ) -> list:
        """批量处理帧（纯CPU实现）"""
        processed_batch = []

        for frame in frame_batch:
            try:
                processed_frame = opencv_processor(frame)

                # 确保输出格式正确
                if len(processed_frame.shape) == 2:
                    processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_GRAY2BGR)
                elif processed_frame.shape[2] == 4:
                    processed_frame = cv2.cvtColor(processed_frame, cv2.COLOR_BGRA2BGR)

                processed_batch.append(processed_frame)

            except Exception as e:
                logger.error(f"Frame processing error: {e}")
                processed_batch.append(frame)  # 出错时使用原始帧

        return processed_batch
    
    def _merge_audio_with_ffmpeg(self, video_path: str, output_path: str):
        """使用FFmpeg合并音频"""
        try:
            cmd = [
                'ffmpeg', '-i', video_path, '-i', self.input_path,
                '-c:v', 'copy',  # 复制视频流，不重编码
                '-c:a', 'aac',   # 音频编码为AAC
                '-map', '0:v:0', # 使用第一个文件的视频流
                '-map', '1:a:0', # 使用第二个文件的音频流
                '-shortest',     # 以较短的流为准
                '-y',            # 覆盖输出文件
                output_path
            ]
            
            logger.info(f"FFmpeg merge command: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("Audio merging completed successfully")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg audio merging failed: {e.stderr}")
            # 如果音频合并失败，至少保留视频
            import shutil
            shutil.move(video_path, output_path)
            logger.warning("Using video without audio due to merge failure")
        except Exception as e:
            logger.error(f"Audio merging error: {e}")
            import shutil
            shutil.move(video_path, output_path)


def create_opencv_processor(operation_type: str, **parameters):
    """创建OpenCV处理器函数"""
    
    if operation_type == 'grayscale':
        def processor(bgr_frame):
            return cv2.cvtColor(bgr_frame, cv2.COLOR_BGR2GRAY)
        return processor
    
    elif operation_type == 'resize':
        width = parameters.get('width', 640)
        height = parameters.get('height', 480)
        def processor(bgr_frame):
            return cv2.resize(bgr_frame, (width, height))
        return processor
    
    elif operation_type == 'binary':
        threshold = parameters.get('threshold', 127)
        max_value = parameters.get('max_value', 255)
        threshold_type = parameters.get('threshold_type', 'binary')
        
        thresh_types = {
            'binary': cv2.THRESH_BINARY,
            'binary_inv': cv2.THRESH_BINARY_INV,
            'trunc': cv2.THRESH_TRUNC,
            'tozero': cv2.THRESH_TOZERO,
            'tozero_inv': cv2.THRESH_TOZERO_INV
        }
        thresh_type = thresh_types.get(threshold_type, cv2.THRESH_BINARY)
        
        def processor(bgr_frame):
            gray_frame = cv2.cvtColor(bgr_frame, cv2.COLOR_BGR2GRAY)
            _, binary_frame = cv2.threshold(gray_frame, threshold, max_value, thresh_type)
            return binary_frame
        return processor
    
    elif operation_type == 'blur':
        filter_type = parameters.get('filter_type', 'gaussian')
        kernel_size = parameters.get('kernel_size', 5)
        sigma = parameters.get('sigma', 1.0)
        
        # 确保kernel_size为奇数
        if kernel_size % 2 == 0:
            kernel_size += 1
        
        def processor(bgr_frame):
            if filter_type == 'gaussian':
                return cv2.GaussianBlur(bgr_frame, (kernel_size, kernel_size), sigma)
            elif filter_type == 'median':
                return cv2.medianBlur(bgr_frame, kernel_size)
            elif filter_type == 'bilateral':
                return cv2.bilateralFilter(bgr_frame, kernel_size, sigma*20, sigma*20)
            else:
                return bgr_frame
        return processor
    
    elif operation_type == 'edge_detection':
        low_threshold = parameters.get('low_threshold', 50)
        high_threshold = parameters.get('high_threshold', 150)
        edge_type = parameters.get('edge_type', 'canny')
        
        def processor(bgr_frame):
            gray_frame = cv2.cvtColor(bgr_frame, cv2.COLOR_BGR2GRAY)
            
            if edge_type == 'canny':
                edges = cv2.Canny(gray_frame, low_threshold, high_threshold)
            elif edge_type == 'sobel':
                import numpy as np
                sobelx = cv2.Sobel(gray_frame, cv2.CV_64F, 1, 0, ksize=3)
                sobely = cv2.Sobel(gray_frame, cv2.CV_64F, 0, 1, ksize=3)
                edges = np.sqrt(sobelx**2 + sobely**2)
                edges = np.uint8(edges / edges.max() * 255)
            elif edge_type == 'laplacian':
                import numpy as np
                edges = cv2.Laplacian(gray_frame, cv2.CV_64F)
                edges = np.uint8(np.absolute(edges))
            else:
                edges = gray_frame
            
            return edges
        return processor
    
    elif operation_type == 'transform':
        transform_type = parameters.get('transform_type', 'rotate_90')
        angle = parameters.get('angle', 90)
        
        def processor(bgr_frame):
            height, width = bgr_frame.shape[:2]

            if transform_type == 'rotate_90':
                return cv2.rotate(bgr_frame, cv2.ROTATE_90_CLOCKWISE)
            elif transform_type == 'rotate_180':
                return cv2.rotate(bgr_frame, cv2.ROTATE_180)
            elif transform_type == 'rotate_270':
                return cv2.rotate(bgr_frame, cv2.ROTATE_90_COUNTERCLOCKWISE)
            elif transform_type == 'flip_horizontal':
                return cv2.flip(bgr_frame, 1)
            elif transform_type == 'flip_vertical':
                return cv2.flip(bgr_frame, 0)
            elif transform_type == 'flip_both':
                return cv2.flip(bgr_frame, -1)
            elif transform_type == 'rotate_custom':
                center = (width // 2, height // 2)
                rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
                return cv2.warpAffine(bgr_frame, rotation_matrix, (width, height))
            else:
                return cv2.rotate(bgr_frame, cv2.ROTATE_90_CLOCKWISE)
        return processor
    
    else:
        # 默认返回原始帧
        def processor(bgr_frame):
            return bgr_frame
        return processor