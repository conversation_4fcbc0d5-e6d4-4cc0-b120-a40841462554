"""
Tasks package initialization - 重构后的任务注册
"""

def register_tasks(celery_app):
    """注册所有任务到celery应用"""
    
    # 图像处理任务
    from .image_sharpen import sharpen_image_task
    from .image_gamma_correction import gamma_correction_task
    from .image_grayscale import grayscale_image_task
    from .image_edge_detection import canny_edge_detection_task
    from .image_fusion import image_fusion_task
    
    # 高级图像处理任务
    from .image_stitching import image_stitching_task
    from .texture_transfer import texture_transfer_task

    # 美颜处理任务
    from .beauty_processing import beauty_enhancement_task
    
    # 视频处理任务
    from .video_grayscale import grayscale_video_task
    from .video_resize import resize_video_task
    from .video_binary import binary_video_task
    from .video_blur import blur_video_task
    from .video_edge_detection import edge_detection_video_task
    from .video_transform import transform_video_task
    from .video_extract_frame import extract_video_frame_task
    from .video_tasks import process_video_task
    
    # 注册图像处理任务
    sharpen_image = celery_app.task(bind=True)(sharpen_image_task)
    gamma_correction = celery_app.task(bind=True)(gamma_correction_task)
    grayscale_image = celery_app.task(bind=True)(grayscale_image_task)
    canny_edge_detection = celery_app.task(bind=True)(canny_edge_detection_task)
    image_fusion = celery_app.task(bind=True)(image_fusion_task)
    
    # 注册高级图像处理任务
    image_stitching = celery_app.task(bind=True)(image_stitching_task)
    texture_transfer = celery_app.task(bind=True)(texture_transfer_task)

    # 注册美颜处理任务
    beauty_enhancement = celery_app.task(bind=True)(beauty_enhancement_task)
    
    # 注册视频处理任务
    grayscale_video = celery_app.task(bind=True)(grayscale_video_task)
    resize_video = celery_app.task(bind=True)(resize_video_task)
    binary_video = celery_app.task(bind=True)(binary_video_task)
    blur_video = celery_app.task(bind=True)(blur_video_task)
    edge_detection_video = celery_app.task(bind=True)(edge_detection_video_task)
    transform_video = celery_app.task(bind=True)(transform_video_task)
    extract_video_frame = celery_app.task(bind=True)(extract_video_frame_task)
    process_video_task = celery_app.task(bind=True)(process_video_task)
    
    # 创建任务字典以便导出
    tasks = {
        # 图像处理任务
        'sharpen_image': sharpen_image,
        'gamma_correction': gamma_correction,
        'grayscale_image': grayscale_image,
        'canny_edge_detection': canny_edge_detection,
        'image_fusion': image_fusion,
        
        # 高级图像处理任务
        'image_stitching': image_stitching,
        'texture_transfer': texture_transfer,

        # 美颜处理任务
        'beauty_enhancement': beauty_enhancement,
        
        # 视频处理任务
        'grayscale_video': grayscale_video,
        'resize_video': resize_video,
        'binary_video': binary_video,
        'blur_video': blur_video,
        'edge_detection_video': edge_detection_video,
        'transform_video': transform_video,
        'extract_video_frame': extract_video_frame,
        'process_video_task': process_video_task,
    }
    
    return tasks