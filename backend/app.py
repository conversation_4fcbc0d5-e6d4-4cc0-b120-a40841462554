"""
重构后的主应用文件
"""
import logging
from flask import Flask
from flask_cors import CORS
from celery import Celery

from config import CORS_ORIGINS, CELERY_CONFIG, UPLOAD_FOLDER, OUTPUT_FOLDER

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 配置CORS
CORS(app, origins=CORS_ORIGINS, supports_credentials=True,
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

# 配置应用
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB

# 创建Celery实例
celery = Celery(app.name)
celery.conf.update(CELERY_CONFIG)

# 注册任务模块
try:
    from tasks import register_tasks
    tasks = register_tasks(celery)
    logger.info("Tasks registered successfully")
except ImportError as e:
    logger.error(f"Failed to register tasks: {e}")
    tasks = None

# 注册蓝图
from routes.upload import upload_bp
from routes.preview import preview_bp
from routes.download import download_bp
from routes.process import process_bp
from routes.system import system_bp

app.register_blueprint(upload_bp)
app.register_blueprint(preview_bp)
app.register_blueprint(download_bp)
app.register_blueprint(process_bp)
app.register_blueprint(system_bp)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
