"""
任务处理路由
"""
import os
import logging
from flask import Blueprint, request, jsonify

from config import UPLOAD_FOLDER
from utils.validation import validate_file_path, validate_parameters

logger = logging.getLogger(__name__)

process_bp = Blueprint('process', __name__)

# 内存中的批处理存储（生产环境应使用Redis）
batch_storage = {}


@process_bp.route('/api/process', methods=['POST'])
def process_files():
    """文件处理接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        files = data.get('files', [])
        operation = data.get('operation')
        parameters = data.get('parameters', {})

        if not files or not operation:
            return jsonify({'error': 'Missing files or operation'}), 400

        # 验证操作参数
        is_valid, message = validate_parameters(operation, parameters)
        if not is_valid:
            return jsonify({'error': f'Invalid parameters: {message}'}), 400

        task_ids = []
        failed_files = []

        # 处理双文件操作（如图像融合、纹理迁移）
        if operation in ['image_fusion', 'texture_transfer']:
            if len(files) % 2 != 0:
                return jsonify({'error': f'Operation {operation} requires pairs of files'}), 400

            for i in range(0, len(files), 2):
                file1_info = files[i]
                file2_info = files[i + 1] if i + 1 < len(files) else None

                if not file2_info:
                    continue

                # 从stored_filename构造正确的文件路径
                file1_stored = file1_info.get('stored_filename')
                file2_stored = file2_info.get('stored_filename')
                
                if not file1_stored or not file2_stored:
                    continue

                file1_path = os.path.abspath(os.path.join(UPLOAD_FOLDER, file1_stored))
                file2_path = os.path.abspath(os.path.join(UPLOAD_FOLDER, file2_stored))

                # 验证两个文件路径
                abs_upload_folder = os.path.abspath(UPLOAD_FOLDER)
                if (not validate_file_path(file1_path, abs_upload_folder) or
                    not validate_file_path(file2_path, abs_upload_folder) or
                    not os.path.exists(file1_path) or not os.path.exists(file2_path)):
                    continue

                try:
                    task = None
                    # 从app中获取已注册的任务
                    from app import tasks

                    if operation == 'image_fusion':
                        task = tasks['image_fusion'].delay(file1_path, file2_path, **parameters)
                    elif operation == 'texture_transfer':
                        task = tasks['texture_transfer'].delay(file1_path, file2_path, parameters)

                    if task:
                        task_ids.append({
                            'task_id': task.id,
                            'file1': file1_info.get('filename', 'unknown'),
                            'file2': file2_info.get('filename', 'unknown'),
                            'operation': operation
                        })
                except Exception as e:
                    logger.error(f"Failed to process files {file1_path}, {file2_path}: {str(e)}")
                    failed_files.append({
                        'file1': file1_info.get('filename', 'unknown'),
                        'file2': file2_info.get('filename', 'unknown'),
                        'error': str(e)
                    })
        elif operation in ['image_stitching']:
            # 处理多文件操作（图像拼接）
            if len(files) < 2:
                return jsonify({'error': f'Operation {operation} requires at least 2 files'}), 400

            # 收集所有文件路径
            file_paths = []
            for file_info in files:
                stored_filename = file_info.get('stored_filename')
                if not stored_filename:
                    continue

                file_path = os.path.abspath(os.path.join(UPLOAD_FOLDER, stored_filename))

                # 验证文件路径
                if not validate_file_path(file_path, os.path.abspath(UPLOAD_FOLDER)):
                    continue
                if not os.path.exists(file_path):
                    continue

                file_paths.append(file_path)

            if len(file_paths) < 2:
                return jsonify({'error': 'Not enough valid files for stitching'}), 400

            try:
                # 从app中获取已注册的任务
                from app import tasks
                task = tasks['image_stitching'].delay(file_paths, parameters)

                if task:
                    task_ids.append({
                        'task_id': task.id,
                        'files': [f.get('filename', 'unknown') for f in files],
                        'operation': operation
                    })
            except Exception as e:
                logger.error(f"Failed to process stitching files: {str(e)}")
                failed_files.append({
                    'files': [f.get('filename', 'unknown') for f in files],
                    'error': str(e)
                })
        else:
            # 处理单文件操作
            for file_info in files:
                logger.info(f"Processing file_info: {file_info}")
                
                # 从file_info中获取stored_filename并构造完整路径
                stored_filename = file_info.get('stored_filename')
                if not stored_filename:
                    logger.warning(f"No stored_filename in file_info: {file_info}")
                    continue
                
                # 构造正确的绝对文件路径
                file_path = os.path.join(UPLOAD_FOLDER, stored_filename)
                file_path = os.path.abspath(file_path)  # 确保为绝对路径
                
                logger.info(f"Constructed file_path: {file_path}")
                
                # 验证文件路径
                if not validate_file_path(file_path, os.path.abspath(UPLOAD_FOLDER)):
                    logger.warning(f"File path validation failed: {file_path}, UPLOAD_FOLDER: {os.path.abspath(UPLOAD_FOLDER)}")
                    continue

                if not os.path.exists(file_path):
                    logger.warning(f"File does not exist: {file_path}")
                    continue

                logger.info(f"File validated successfully: {file_path}")

                try:
                    task = None
                    # 从app中获取已注册的任务
                    from app import tasks
                    
                    if tasks is None:
                        logger.error("Tasks not registered, skipping file processing")
                        failed_files.append({
                            'filename': file_info.get('filename', 'unknown'),
                            'error': 'Tasks not registered'
                        })
                        continue
                    
                    logger.info(f"Available tasks: {list(tasks.keys()) if tasks else 'None'}")
                    
                    # 根据操作类型动态选择任务
                    # 图像专用操作
                    if operation == 'sharpen':
                        task = tasks['sharpen_image'].delay(file_path, parameters)
                    elif operation == 'gamma_correction':
                        task = tasks['gamma_correction'].delay(file_path, parameters)
                    elif operation == 'canny_edge':
                        task = tasks['canny_edge_detection'].delay(file_path, parameters)
                    elif operation == 'beauty_enhancement':
                        task = tasks['beauty_enhancement'].delay(file_path, parameters)
                    # 视频操作（包括grayscale）
                    elif operation in ['resize', 'grayscale', 'binary', 'blur', 'edge_detection', 'transform', 'extract_frame']:
                        # 所有视频操作都通过process_video_task处理
                        task = tasks['process_video_task'].delay(
                            file_path=file_path,
                            operation=operation,
                            parameters=parameters
                        )

                    if task:
                        task_ids.append({
                            'task_id': task.id,
                            'filename': file_info.get('filename', 'unknown'),
                            'operation': operation
                        })
                except Exception as e:
                    logger.error(f"Failed to process file {file_path}: {str(e)}")
                    failed_files.append({
                        'filename': file_info.get('filename', 'unknown'),
                        'error': str(e)
                    })

        if not task_ids:
            return jsonify({'error': 'No tasks were submitted successfully'}), 400

        # 生成批处理ID
        import uuid
        batch_id = str(uuid.uuid4())

        # 存储批处理信息
        batch_storage[batch_id] = {
            'task_ids': [task['task_id'] for task in task_ids],
            'operation': operation,
            'total_tasks': len(task_ids),
            'failed_files': failed_files
        }

        return jsonify({
            'batch_id': batch_id,
            'task_ids': task_ids,
            'total_tasks': len(task_ids),
            'failed_files': failed_files,
            'message': f'{operation} submitted successfully'
        })

    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        return jsonify({'error': 'Processing failed'}), 500


@process_bp.route('/api/process/status', methods=['POST'])
def get_process_status():
    """查询任务状态"""
    try:
        data = request.get_json()
        if not data or 'task_ids' not in data:
            return jsonify({'error': 'No task IDs provided'}), 400

        task_ids = data['task_ids']
        if not isinstance(task_ids, list):
            return jsonify({'error': 'task_ids must be a list'}), 400

        # 导入celery实例
        from app import celery

        results = []
        for task_id in task_ids:
            try:
                task = celery.AsyncResult(task_id)
                status = task.status
                result = None
                progress = 0

                if status == 'SUCCESS':
                    result = task.result
                    progress = 100
                elif status == 'FAILURE':
                    result = str(task.info)
                    progress = 0
                elif status == 'PENDING':
                    progress = 0
                elif status == 'PROGRESS':
                    if hasattr(task.info, 'get'):
                        progress = task.info.get('progress', 0)
                    else:
                        progress = 50

                results.append({
                    'task_id': task_id,
                    'status': status,
                    'result': result,
                    'progress': progress
                })
            except Exception as e:
                logger.error(f"Error getting status for task {task_id}: {str(e)}")
                results.append({
                    'task_id': task_id,
                    'status': 'ERROR',
                    'result': f'Status query error: {str(e)}',
                    'progress': 0
                })

        return jsonify({
            'results': results,
            'total_tasks': len(task_ids)
        })

    except Exception as e:
        logger.error(f"Status query error: {str(e)}")
        return jsonify({'error': 'Failed to query status'}), 500


@process_bp.route('/api/process/results', methods=['POST'])
def get_process_results():
    """获取处理结果"""
    try:
        data = request.get_json()
        if not data or 'task_ids' not in data:
            return jsonify({'error': 'No task IDs provided'}), 400

        task_ids = data['task_ids']
        if not isinstance(task_ids, list):
            return jsonify({'error': 'task_ids must be a list'}), 400

        # 导入celery实例
        from app import celery

        results = []
        for task_id in task_ids:
            try:
                task = celery.AsyncResult(task_id)
                status = task.status

                if status == 'SUCCESS':
                    try:
                        result = task.result
                        results.append({
                            'task_id': task_id,
                            'status': status,
                            'result': result
                        })
                    except Exception as e:
                        logger.error(f"Error getting result for task {task_id}: {str(e)}")
                        results.append({
                            'task_id': task_id,
                            'status': 'ERROR',
                            'error': f'Result retrieval error: {str(e)}'
                        })
                else:
                    results.append({
                        'task_id': task_id,
                        'status': status,
                        'result': None
                    })
            except Exception as e:
                logger.error(f"Error processing task {task_id}: {str(e)}")
                results.append({
                    'task_id': task_id,
                    'status': 'ERROR',
                    'error': f'Task processing error: {str(e)}'
                })

        return jsonify({
            'results': results,
            'total_tasks': len(task_ids)
        })

    except Exception as e:
        logger.error(f"Results query error: {str(e)}")
        return jsonify({'error': 'Failed to get results'}), 500
