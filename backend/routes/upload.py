"""
文件上传路由
"""
import os
import uuid
import logging
from flask import Blueprint, request, jsonify
from werkzeug.utils import secure_filename

from config import UPLOAD_FOLDER, MAX_CONTENT_LENGTH, ALLOWED_EXTENSIONS
from utils.validation import allowed_file, validate_file_path

logger = logging.getLogger(__name__)

upload_bp = Blueprint('upload', __name__)


@upload_bp.route('/api/upload', methods=['POST'])
def upload_files():
    """文件上传接口"""
    try:
        logger.info(f"Multiple file upload request received. Request files: {list(request.files.keys())}")

        if 'files' not in request.files:
            logger.error("No 'files' field in request")
            return jsonify({'error': 'No files provided'}), 400

        files = request.files.getlist('files')

        if not files or all(f.filename == '' for f in files):
            logger.error(f"No valid files found. Files: {[f.filename for f in files]}")
            return jsonify({'error': 'No files selected'}), 400

        uploaded_files = []
        total_size = 0

        for file in files:
            if file.filename == '':
                continue

            original_filename = file.filename
            file_type = request.form.get('file_type', 'image')

            # 检查文件类型
            if not allowed_file(original_filename, file_type):
                return jsonify({
                    'error': f'File type not allowed for {original_filename}. Supported {file_type} formats: {", ".join(ALLOWED_EXTENSIONS[file_type])}'
                }), 400

            # 安全的文件名处理
            filename = secure_filename(original_filename)
            if not filename:
                return jsonify({'error': f'Invalid filename: {original_filename}'}), 400

            file_id = str(uuid.uuid4())
            file_extension = os.path.splitext(original_filename)[1].lower()
            new_filename = f"{file_id}{file_extension}"
            file_path = os.path.join(UPLOAD_FOLDER, new_filename)

            # 验证文件路径安全性
            if not validate_file_path(file_path, UPLOAD_FOLDER):
                return jsonify({'error': f'Invalid file path for {original_filename}'}), 400

            # 保存文件
            file.save(file_path)

            file_size = os.path.getsize(file_path)
            total_size += file_size

            if file_size > MAX_CONTENT_LENGTH:
                os.remove(file_path)
                return jsonify({'error': f'File {original_filename} too large'}), 413

            uploaded_files.append({
                'file_id': file_id,
                'filename': filename,
                'original_filename': original_filename,
                'stored_filename': new_filename,
                'file_type': file_type,
                'file_size': file_size
            })

        if not uploaded_files:
            return jsonify({'error': 'No valid files uploaded'}), 400

        # 检查总大小
        if total_size > MAX_CONTENT_LENGTH * 5:  # 允许多文件总大小为单文件限制的5倍
            for file_info in uploaded_files:
                os.remove(file_info['file_path'])
            return jsonify({'error': 'Total file size too large'}), 413

        logger.info(f"Multiple files uploaded successfully: {len(uploaded_files)} files, total size: {total_size} bytes")

        return jsonify({
            'files': uploaded_files,
            'total_files': len(uploaded_files),
            'total_size': total_size,
            'message': 'Files uploaded successfully'
        })

    except Exception as e:
        logger.error(f"Multiple file upload error: {str(e)}")
        return jsonify({'error': 'Multiple file upload failed'}), 500
